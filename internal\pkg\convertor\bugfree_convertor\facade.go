package bugfree_convertor

import "sync"

var (
	bugfreeUserConvertor *BugfreeUserConvertor
	bugfreeUserOnce      sync.Once
)

func NewBugfreeUserConvertor() *BugfreeUserConvertor {
	bugfreeUserOnce.Do(func() {
		bugfreeUserConvertor = new(BugfreeUserConvertor)
	})
	return bugfreeUserConvertor
}

var (
	bugfreeCategoryConvertor *BugfreeCategoryConvertor
	bugfreeCategoryOnce      sync.Once
)

func NewBugfreeCategoryConvertor() *BugfreeCategoryConvertor {
	bugfreeCategoryOnce.Do(func() {
		bugfreeCategoryConvertor = new(BugfreeCategoryConvertor)
	})
	return bugfreeCategoryConvertor
}

var (
	bugfreeSearchConvertor *BugfreeSearchConvertor
	bugfreeSearchOnce      sync.Once
)

func NewBugfreeSearchConvertor() *BugfreeSearchConvertor {
	bugfreeSearchOnce.Do(func() {
		bugfreeSearchConvertor = new(BugfreeSearchConvertor)
	})
	return bugfreeSearchConvertor
}

var (
	bugfreeInfoConvertor *BugfreeInfoConvertor
	bugfreeInfoOnce      sync.Once
)

func NewBugfreeInfoConvertor() *BugfreeInfoConvertor {
	bugfreeInfoOnce.Do(func() {
		bugfreeInfoConvertor = new(BugfreeInfoConvertor)
	})
	return bugfreeInfoConvertor
}

var (
	bugfreeRepliesConvertor *BugfreeRepliesConvertor
	bugfreeRepliesOnce      sync.Once
)

func NewBugfreeRepliesConvertor() *BugfreeRepliesConvertor {
	bugfreeRepliesOnce.Do(func() {
		bugfreeRepliesConvertor = new(BugfreeRepliesConvertor)
	})
	return bugfreeRepliesConvertor
}

var (
	bugfreeReplyConvertor *BugfreeReplyConvertor
	bugfreeReplyOnce      sync.Once
)

func NewBugfreeReplyConvertor() *BugfreeReplyConvertor {
	bugfreeReplyOnce.Do(func() {
		bugfreeReplyConvertor = new(BugfreeReplyConvertor)
	})
	return bugfreeReplyConvertor
}

var (
	bugfreeDuplicateReplyConvertor *BugfreeDuplicateReplyConvertor
	bugfreeDuplicateReplyOnce      sync.Once
)

func NewBugfreeDuplicateReplyConvertor() *BugfreeDuplicateReplyConvertor {
	bugfreeDuplicateReplyOnce.Do(func() {
		bugfreeDuplicateReplyConvertor = new(BugfreeDuplicateReplyConvertor)
	})
	return bugfreeDuplicateReplyConvertor
}

var (
	bugfreeRepeatConvertor *BugfreeRepeatConvertor
	bugfreeRepeatOnce      sync.Once
)

func NewBugfreeRepeatConvertor() *BugfreeRepeatConvertor {
	bugfreeRepeatOnce.Do(func() {
		bugfreeRepeatConvertor = new(BugfreeRepeatConvertor)
	})
	return bugfreeRepeatConvertor
}
