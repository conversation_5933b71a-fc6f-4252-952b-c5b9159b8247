package prototype_manage

import (
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/prototype/prototype_manage/dto"
	"marketing-app/internal/pkg/convertor/prototype_convertor"
	"marketing-app/internal/router/prototype/prototype_manage/client"
	service "marketing-app/internal/service/prototype/prototype_manage"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Prototype interface {
	CreatePrototype(c *gin.Context)
	ListPrototype(c *gin.Context)
	DeletePrototype(c *gin.Context)
}

type prototype struct {
	prototypeSvc service.PrototypeSvc
}

func NewPrototypeHandler(svc service.PrototypeSvc) Prototype {
	return &prototype{prototypeSvc: svc}
}

func (p *prototype) CreatePrototype(c *gin.Context) {
	var (
		req  client.CreatePrototypeRequest
		err  error
		resp dto.CreatePrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}
	err = p.prototypeSvc.CreatePrototype(
		c,
		prototype_convertor.NewPrototypeCreateConvertor().ClientToEntity(&req),
	)
	resp = dto.CreatePrototypeResp{
		Message: "添加成功！",
	}
}

func (p *prototype) ListPrototype(c *gin.Context) {
	var (
		req  client.ListPrototypeRequest
		err  error
		resp *dto.PrototypeListResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	modelCategory, data, err := p.prototypeSvc.GetPrototypeListData(
		c,
		prototype_convertor.NewPrototypeListConvertor().ClientToEntity(&req),
	)
	var categoryList []map[string]interface{}
	for _, category := range modelCategory {
		categoryList = append(categoryList, map[string]interface{}{
			"id":   category.ID,
			"name": category.Name,
		})
	}
	resp = &dto.PrototypeListResp{
		Data:          nil,
		Size:          0,
		IsEnd:         false,
		ModelCategory: categoryList,
	}
	if data != nil {
		resp.Data = data.Data
		resp.Size = data.Size
		resp.IsEnd = data.IsEnd
	}
	if resp.Data == nil {
		err = errors.New("未找到数据")
		return
	}
}

func (p *prototype) DeletePrototype(c *gin.Context) {
	var (
		req  client.DeletePrototypeRequest
		err  error
		resp *dto.DeletePrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if req.Barcode == "" || req.EndpointID == 0 {
		err = errors.New("参数错误")
		return
	}
	msg, err := p.prototypeSvc.DeletePrototype(
		c,
		prototype_convertor.NewPrototypeDeleteConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = &dto.DeletePrototypeResp{
		Message: msg,
	}
}
