package middleware

import (
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"marketing-app/internal/pkg/alilog"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	"net/http"
	"time"
)

// 自定义 ResponseWriter 用于捕获响应体
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// InitLogger 初始化日志记录器
func InitLogger(filename string) *logrus.Logger {
	logger := logrus.New()

	// 自定义 JSON 格式
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339Nano,
		FieldMap: logrus.FieldMap{
			logrus.FieldKeyTime:  "time",
			logrus.FieldKeyLevel: "level",
			logrus.FieldKeyMsg:   "message",
		},
	})

	// 设置日志轮转
	logger.SetOutput(&lumberjack.Logger{
		Filename:   filename,
		MaxSize:    100, // MB
		MaxAge:     10,  // 天
		MaxBackups: 3,
		Compress:   true,
	})

	// 只记录 INFO 及以上级别的日志
	logger.SetLevel(logrus.InfoLevel)

	return logger
}

func LogMiddleware(log *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		startTime := time.Now()

		//  捕获请求体
		reqBody := captureRequestData(c)

		// 2. 准备捕获响应体
		blw := &bodyLogWriter{
			body:           bytes.NewBufferString(""),
			ResponseWriter: c.Writer,
		}
		c.Writer = blw

		// 处理请求
		c.Next()

		// 记录请求结束时间
		endTime := time.Now()

		// 计算耗时
		duration := endTime.Sub(startTime)

		// 构建日志数据结构
		logData := logrus.Fields{
			"user_id":     c.GetUint("uid"),
			"username":    c.GetString("username"),
			"name":        c.GetString("name"),
			"role":        c.GetString("role"),
			"phone":       c.GetString("phone"),
			"ip":          c.ClientIP(),
			"method":      c.Request.Method,
			"path":        c.Request.URL.Path,
			"input":       reqBody,
			"output":      json.RawMessage(blw.body.Bytes()),
			"status_code": c.Writer.Status(),
			"latency":     duration.String(),
			"request_id":  c.GetHeader("X-Request-ID"),
			"time":        time.Now().Format(time.DateTime),
		}
		entry := log.WithFields(logData)

		// 打印日志
		go entry.Info("request")
		if config.GetString("GIN_MODE") == "release" {
			go alilog.SendLog("yx-log", "user-log", logData)
		}
	}
}

// captureRequestData 捕获请求数据
func captureRequestData(c *gin.Context) map[string]interface{} {

	params := make(map[string]interface{})

	// 获取 URL 参数
	for k, v := range c.Request.URL.Query() {
		if len(v) == 1 {
			params[k] = v[0]
		} else {
			params[k] = v
		}
	}

	// 获取 POST 表单数据
	if c.Request.Method == http.MethodPost {
		err := c.Request.ParseForm()
		if err != nil {
			log.Error("解析表单数据失败" + err.Error())
		}
		for k, v := range c.Request.PostForm {
			if len(v) == 1 {
				params[k] = v[0]
			} else {
				params[k] = v
			}
		}
	}

	// 获取 JSON 数据
	if c.Request.Body != nil {
		bodyBytes, _ := io.ReadAll(c.Request.Body)

		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		var jsonData map[string]interface{}
		if json.Unmarshal(bodyBytes, &jsonData) == nil {
			for k, v := range jsonData {
				params[k] = v
			}
		}
	}

	return utils.MaskMap(
		params,
		[]string{"password", "token", "secret"},
	)
}
