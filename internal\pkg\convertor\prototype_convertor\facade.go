package prototype_convertor

import "sync"

var (
	prototypeCreateConvertor *PrototypeCreateConvertor
	prototypeCreateOnce      sync.Once
)

func NewPrototypeCreateConvertor() *PrototypeCreateConvertor {
	prototypeCreateOnce.Do(func() {
		prototypeCreateConvertor = new(PrototypeCreateConvertor)
	})
	return prototypeCreateConvertor
}

var (
	prototypeListConvertor *PrototypeListConvertor
	prototypeListOnce      sync.Once
)

func NewPrototypeListConvertor() *PrototypeListConvertor {
	prototypeListOnce.Do(func() {
		prototypeListConvertor = new(PrototypeListConvertor)
	})
	return prototypeListConvertor
}

var (
	prototypeDeleteConvertor *PrototypeDeleteConvertor
	prototypeDeleteOnce      sync.Once
)

func NewPrototypeDeleteConvertor() *PrototypeDeleteConvertor {
	prototypeDeleteOnce.Do(func() {
		prototypeDeleteConvertor = new(PrototypeDeleteConvertor)
	})
	return prototypeDeleteConvertor
}
