package model

import (
	"time"
)

type BugfreeMachine struct {
	ID        int         `gorm:"column:id;primaryKey;autoIncrement;comment:主键"`
	Order     int         `gorm:"column:order;not null;default:0"`
	Title     string      `gorm:"column:title;type:varchar(50);not null;default:'';comment:机型"`
	ParentID  int         `gorm:"column:parent_id;not null;default:0;comment:父类id"`
	Status    int         `gorm:"column:status;not null;default:0;comment:状态，0为不可见，1为可见"`
	Type      MachineType `gorm:"column:type;type:enum('tablet','watch','scanning_pen','study_desk','study_card');not null;comment:机器类型"`
	CreatedAt time.Time   `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time   `gorm:"column:updated_at;type:timestamp;not null"`
}

func (BugfreeMachine) TableName() string {
	return "bugfree_machine"
}
