package bugfree

import (
	bugfreeClient "marketing-app/internal/api/client/bugfree"
	adminUserRepo "marketing-app/internal/repository"
	repository "marketing-app/internal/repository/bugfree"
	"marketing-app/internal/service/bugfree/entity"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type BugfreeSvc interface {
	GetBugfreeUser(c *gin.Context, param *entity.BugfreeUser) (interface{}, error)
	GetBugfreeCategory(c *gin.Context, param *entity.BugfreeCategory) (data interface{}, machine *entity.MachineType, err error)
	GetBugfreeSearch(c *gin.Context, param *entity.BugfreeSearch) (interface{}, error)
	GetBugfreeInfo(c *gin.Context, param *entity.BugfreeInfo) (interface{}, error)
	GetBugfreeReplies(c *gin.Context, param *entity.BugfreeReplies) (interface{}, error)
	PostBugfreeReply(c *gin.Context, param *entity.BugfreeReply) error
	PostBugfreeDuplicateReply(c *gin.Context, param *entity.BugfreeDuplicateReply) (interface{}, error)
	PostBugfreeRepeat(c *gin.Context, param *entity.BugfreeRepeat) (interface{}, error)
	GetTextbookResource(c *gin.Context) (interface{}, error)
	GetSubjectGrade(c *gin.Context) (interface{}, error)
}

type bugfree struct {
	bugfreeRepo   repository.BugfreeRepo
	adminUserRepo adminUserRepo.AdminUserRepository
	bugfreeClient bugfreeClient.BugfreeClient
}

func NewBugfree(
	bugfreeRepo repository.BugfreeRepo,
	bugfreeClient bugfreeClient.BugfreeClient,
	adminUserRepo adminUserRepo.AdminUserRepository,
) BugfreeSvc {
	return &bugfree{
		bugfreeRepo:   bugfreeRepo,
		bugfreeClient: bugfreeClient,
		adminUserRepo: adminUserRepo,
	}
}

func (b *bugfree) GetBugfreeUser(c *gin.Context, param *entity.BugfreeUser) (interface{}, error) {
	// 获取用户信息
	data, err := b.bugfreeClient.GetUser(c, param.Uid, param.Username)
	if err != nil {
		return nil, errors.Wrap(err, "获取用户信息失败")
	}
	if data == nil {
		return nil, errors.New("未找到数据")
	}

	return data, nil
}

func (b *bugfree) GetBugfreeCategory(c *gin.Context, param *entity.BugfreeCategory) (data interface{}, machine *entity.MachineType, err error) {
	rolesKey, err := b.checkRoles(c, param.Uid, -1)
	if err != nil {
		return nil, nil, err
	}
	data, err = b.bugfreeClient.GetBugfreeCategory(c, param, rolesKey)
	if err != nil {
		return nil, nil, err
	}
	machine, err = b.getMachineType(c)
	if err != nil {
		err = errors.Wrap(err, "获取机器类型失败")
		return nil, nil, err
	}
	return data, machine, nil
}

func (b *bugfree) checkRoles(c *gin.Context, uid int, appType int) (rolesKey map[string]struct{}, err error) {
	rolesKey = make(map[string]struct{})
	roles, err := b.adminUserRepo.CheckRole(c, uid, appType)
	if err != nil || len(roles) == 0 {
		err = errors.New("未查到uid用户权限")
		return nil, err
	}

	if len(roles) > 0 {
		for _, role := range roles {
			if _, exist := rolesKey[role.Slug]; !exist {
				rolesKey[role.Slug] = struct{}{}
			}
		}
	}

	return rolesKey, nil
}

func (b *bugfree) getMachineType(c *gin.Context) (machineType *entity.MachineType, err error) {
	bugfreeMachine, err := b.bugfreeRepo.GetMachineType(c)
	if err != nil {
		return nil, err
	}
	if len(bugfreeMachine) == 0 {
		return nil, nil
	}
	title := []string{"其他(请在问题详情中描述)"}
	detail := []map[string]string{
		{"title": "其他(请在问题详情中描述)", "type": "tablet"},
	}
	for _, m := range bugfreeMachine {
		title = append(title, m.Title)
		detail = append(detail, map[string]string{
			"title": m.Title,
			"type":  m.Type,
		})
	}
	return &entity.MachineType{
		Titles: title,
		Detail: detail,
	}, nil
}

func (b *bugfree) GetBugfreeSearch(c *gin.Context, param *entity.BugfreeSearch) (interface{}, error) {
	rolesKey, err := b.checkRoles(c, param.Uid, -1)
	if err != nil {
		return nil, err
	}

	data, err := b.bugfreeClient.GetBugfreeSearch(c, param, rolesKey)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (b *bugfree) GetBugfreeInfo(c *gin.Context, param *entity.BugfreeInfo) (interface{}, error) {
	rolesKey, err := b.checkRoles(c, param.Uid, -1)
	if err != nil {
		return nil, err
	}

	// 获取反馈详情
	info, err := b.bugfreeClient.GetBugfreeInfo(c, param, rolesKey)
	if err != nil {
		return nil, err
	}

	// 获取回复列表
	replies, err := b.bugfreeClient.GetBugfreeReplies(c, param.Id, 1, 10, rolesKey)
	if err != nil {
		return nil, err
	}

	// 获取处理流程
	processRoutes, err := b.bugfreeClient.GetBugfreeProcessRoutes(c, param.Id, rolesKey)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	result := map[string]interface{}{
		"info":           info,
		"replies":        replies,
		"process_routes": processRoutes,
	}

	return result, nil
}

func (b *bugfree) GetBugfreeReplies(c *gin.Context, param *entity.BugfreeReplies) (interface{}, error) {
	rolesKey, err := b.checkRoles(c, param.Uid, -1)
	if err != nil {
		return nil, err
	}

	replies, err := b.bugfreeClient.GetBugfreeReplies(c, param.Id, param.Page, param.Count, rolesKey)
	if err != nil {
		return nil, err
	}

	result := map[string]interface{}{
		"replies": replies,
	}

	return result, nil
}

func (b *bugfree) PostBugfreeReply(c *gin.Context, param *entity.BugfreeReply) error {
	rolesKey, err := b.checkRoles(c, param.Uid, -1)
	if err != nil {
		return err
	}
	code, err := b.bugfreeClient.ReplyBugfree(c, param.Id, param.Uid, param.Content, rolesKey)
	if err != nil {
		if code == 2 { // 业务错误
			return err
		}
		return errors.Wrap(err, "内部错误")
	}
	if code != 0 {
		return errors.New("内部错误")
	}
	return nil
}

func (b *bugfree) PostBugfreeDuplicateReply(c *gin.Context, param *entity.BugfreeDuplicateReply) (interface{}, error) {
	rolesKey, err := b.checkRoles(c, param.Uid, -1)
	if err != nil {
		return nil, err
	}

	// 构建请求内容
	content := map[string]interface{}{
		"user_id":      param.Uid,
		"username":     param.Username,
		"phone_number": param.PhoneNumber,
		"id":           param.Id, // bug id
	}

	// 添加可选字段
	if param.Content != "" {
		content["content"] = param.Content
	}
	if len(param.Attachments) > 0 {
		content["attachments"] = param.Attachments
	}

	data, err := b.bugfreeClient.DuplicateReplyBugfree(c, param.Id, content, rolesKey)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (b *bugfree) PostBugfreeRepeat(c *gin.Context, param *entity.BugfreeRepeat) (interface{}, error) {
	// 构建请求内容
	content := map[string]interface{}{
		"user_id": param.UID,
	}

	data, err := b.bugfreeClient.RepeatBugfree(c, param.Id, content)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (b *bugfree) GetTextbookResource(c *gin.Context) (interface{}, error) {
	// 从数据库获取教材资源标签
	data, err := b.bugfreeRepo.GetTextbookLabels(c)
	if err != nil {
		return nil, errors.Wrap(err, "获取教材资源标签失败")
	}
	if len(data) == 0 {
		return nil, errors.New("未找到数据")
	}

	return data, nil
}

func (b *bugfree) GetSubjectGrade(c *gin.Context) (interface{}, error) {
	// 从数据库获取学科、年级等数据
	subject, err := b.bugfreeRepo.GetSubjects(c)
	if err != nil {
		return nil, errors.Wrap(err, "获取学科数据失败")
	}

	// 获取学习阶段数据（虽然不返回给前端，但需要验证数据获取是否正常）
	_, err = b.bugfreeRepo.GetStages(c)
	if err != nil {
		return nil, errors.Wrap(err, "获取学习阶段数据失败")
	}

	grade, err := b.bugfreeRepo.GetGrades(c)
	if err != nil {
		return nil, errors.Wrap(err, "获取年级数据失败")
	}

	// 组装返回数据，与原Python代码逻辑一致
	result := map[string]interface{}{
		"subject":         subject,
		"grade":           grade,
		"resource_volume": []string{"上册", "下册", "全册"}, // 固定的资源册数选项
	}

	return result, nil
}
