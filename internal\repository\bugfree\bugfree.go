package bugfree

import (
	"marketing-app/internal/model"
	"marketing-app/internal/repository/bugfree/dto"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BugfreeRepo interface {
	GetMachineType(c *gin.Context) (data []dto.BugfreeMachine, err error)
	GetTextbookLabels(c *gin.Context) (data []dto.BugfreeTextbookLabel, err error)
	GetSubjects(c *gin.Context) (data []dto.BugfreeSubject, err error)
	GetStages(c *gin.Context) (data []dto.BugfreeStage, err error)
	GetGrades(c *gin.Context) (data []dto.BugfreeGrade, err error)
}

type Bugfree struct {
	db *gorm.DB
}

func NewBugfreeRepository(db *gorm.DB) BugfreeRepo {
	return &Bugfree{
		db: db,
	}
}

func (b *Bugfree) GetMachineType(c *gin.Context) (data []dto.BugfreeMachine, err error) {
	err = b.db.WithContext(c).Model(&model.BugfreeMachine{}).Order("`order`").Find(&data).Error
	return data, err
}

func (b *Bugfree) GetTextbookLabels(c *gin.Context) (data []dto.BugfreeTextbookLabel, err error) {
	err = b.db.WithContext(c).Model(&model.BugfreeLabel{}).
		Select("id, name").
		Where("visibility = ? AND category = ? AND type = ?", 1, 1, 1).
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetSubjects(c *gin.Context) (data []dto.BugfreeSubject, err error) {
	err = b.db.WithContext(c).Model(&model.Subject{}).
		Select("id, name").
		Where("visibility = ?", 1).
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetStages(c *gin.Context) (data []dto.BugfreeStage, err error) {
	err = b.db.WithContext(c).Model(&model.Stage{}).
		Select("id, name").
		Where("visibility = ?", 1).
		Order("`order`").
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetGrades(c *gin.Context) (data []dto.BugfreeGrade, err error) {
	err = b.db.WithContext(c).Model(&model.Grade{}).
		Select("grade.id, grade.name, grade.stage as stage_id, s.name as stage_name").
		Joins("left join stage s on grade.stage = s.id").
		Where("grade.visibility = ?", 1).
		Order("grade.stage, grade.order").
		Find(&data).Error
	return data, err
}
