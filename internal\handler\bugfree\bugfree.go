package bugfree

import (
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/bugfree/dto"
	"marketing-app/internal/pkg/convertor/bugfree_convertor"
	"marketing-app/internal/router/bugfree/client"
	service "marketing-app/internal/service/bugfree"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Bugfree interface {
	GetBugfreeUser(c *gin.Context)
	GetBugFreeCategory(c *gin.Context)
	GetBugfreeSearch(c *gin.Context)
	GetBugfreeInfo(c *gin.Context)
	GetBugfreeReplies(c *gin.Context)
	PostBugfreeReply(c *gin.Context)
	PostBugfreeDuplicateReply(c *gin.Context)
	PostBugfreeRepeat(c *gin.Context)
	GetTextbookResource(c *gin.Context)
	GetSubjectGrade(c *gin.Context)
}

type bugfree struct {
	bugfreeSvc service.BugfreeSvc
}

func NewBugfreeHandler(svc service.BugfreeSvc) Bugfree {
	return &bugfree{bugfreeSvc: svc}
}

func (b *bugfree) GetBugfreeUser(c *gin.Context) {
	var (
		req  client.GetBugfreeUserRequest
		err  error
		resp *dto.BugfreeUserResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeUser(
		c,
		bugfree_convertor.NewBugfreeUserConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeUserResp{
		Data: data,
	}
}

func (b *bugfree) GetBugFreeCategory(c *gin.Context) {
	var (
		req client.BugfreeCategoryRequest
		err error
	)
	resp := dto.BugFreeCategoryResp{Data: map[string]interface{}{}}
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, machine, err := b.bugfreeSvc.GetBugfreeCategory(
		c,
		bugfree_convertor.NewBugfreeCategoryConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp.Data["products"] = data
	resp.Data["machine_type"] = machine.Titles
	resp.Data["machine_type2"] = machine.Detail
}

// GetBugfreeSearch 搜索问题列表，重构了python的实现，企微中的该api在api-pad-feedback-test.readboy.com中 TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) GetBugfreeSearch(c *gin.Context) {
	var (
		req  client.BugfreeSearchRequest
		err  error
		resp *dto.BugfreeSearchResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeSearch(
		c,
		bugfree_convertor.NewBugfreeSearchConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeSearchResp{
		Data: data,
	}
}

// GetBugfreeInfo 获取问题详情，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) GetBugfreeInfo(c *gin.Context) {
	var (
		req  client.BugfreeInfoRequest
		err  error
		resp *dto.BugfreeInfoResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeInfo(
		c,
		bugfree_convertor.NewBugfreeInfoConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeInfoResp{
		Data: data,
	}
}

// GetBugfreeReplies 获取问题回复，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) GetBugfreeReplies(c *gin.Context) {
	var (
		req  client.BugfreeRepliesRequest
		err  error
		resp *dto.BugfreeRepliesResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeReplies(
		c,
		bugfree_convertor.NewBugfreeRepliesConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeRepliesResp{
		Data: data,
	}
}

// PostBugfreeReply 提交回复，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeReply(c *gin.Context) {
	var (
		req  client.BugfreeReplyRequest
		err  error
		resp *dto.BugfreeReplyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	err = b.bugfreeSvc.PostBugfreeReply(
		c,
		bugfree_convertor.NewBugfreeReplyConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = &dto.BugfreeReplyResp{Data: true}
}

// PostBugfreeDuplicateReply 标记问题重复并回复，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeDuplicateReply(c *gin.Context) {
	var (
		req  client.BugfreeDuplicateReplyRequest
		err  error
		resp *dto.BugfreeDuplicateReplyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	// 验证必需字段
	if req.Id == 0 {
		err = errors.New("bug反馈ID不能为空")
		return
	}
	if req.Username == "" {
		err = errors.New("用户名不能为空")
		return
	}
	if req.PhoneNumber == "" {
		err = errors.New("手机号码不能为空")
		return
	}

	data, err := b.bugfreeSvc.PostBugfreeDuplicateReply(
		c,
		bugfree_convertor.NewBugfreeDuplicateReplyConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	resp = &dto.BugfreeDuplicateReplyResp{
		Data: data,
	}
}

// PostBugfreeRepeat 问题我也遇到，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeRepeat(c *gin.Context) {
	var (
		req  client.BugfreeRepeatRequest
		err  error
		resp *dto.BugfreeRepeatResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 验证必需字段
	if req.Id == 0 {
		err = errors.New("bug反馈ID不能为空")
		return
	}
	if req.UID == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	data, err := b.bugfreeSvc.PostBugfreeRepeat(
		c,
		bugfree_convertor.NewBugfreeRepeatConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	resp = &dto.BugfreeRepeatResp{
		Data: data,
	}
}

// GetTextbookResource 获取教材资源标签，重构了python的实现
func (b *bugfree) GetTextbookResource(c *gin.Context) {
	var (
		err  error
		resp *dto.TextbookResourceResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	data, err := b.bugfreeSvc.GetTextbookResource(c)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.TextbookResourceResp{
		Data: data,
	}
}

// GetSubjectGrade 获取学科年级信息，重构了python的实现
func (b *bugfree) GetSubjectGrade(c *gin.Context) {
	var (
		err  error
		resp *dto.SubjectGradeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	data, err := b.bugfreeSvc.GetSubjectGrade(c)
	if err != nil {
		return
	}

	resp = &dto.SubjectGradeResp{
		Data: data,
	}
}
