server:
  name: marketing-app
  port: 8088
  mode: debug
  secretKey: sdfewgsdjfwien@3werwe

databases:
  default:
    dsn: "rbcare_test:1AHV6va4begLztln@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_test?charset=utf8mb4&parseTime=True&loc=Local"
    maxOpenConns: 20
    maxIdleConns: 10
    connMaxLifetime: 3600
    level: 3

Logger:
  level: debug
  timeFormat: "2006-01-02 15:04:05"

redis:
  addr: "**************:6379"
  password: ILq38ppdVT96TDtj
  db: 7

warranty:
  warranty_base: "warranty_base_module"
  warranty_entry: "warranty_entry_module"
  warranty_return: "warranty_return_module"
  warranty_exchange: "warranty_exchange_module"

prototype:
  prototype_manage: "prototype_manage_module"

districts:
  host: "http://tiku.readboy.com/api"
  app_secret: "08d9543a320bbf64fe49e9acdf64a04f"
  device_id: "Readboy_yxserver//yx.readboy.com////////"

repair:
  base_url: http://api-repair-test.readboy.com
  app_id: yx.readboy.com
  app_key: 710eb100ff2120cbcde0d78e532de8cg

machine:
  binding_url: https://parentadmin.readboy.com/v1/machine/cancel_bindings
  app_id: parentsadmin
  app_sec: 9b332c2653ce7189da101dac5a63fd4e

bugfree:
  host: http://bugfree2.readboy.com/api
  app_id: 94207226
  app_secret: lqMqWJDvl8kKOJOiWwUPmI1y8tJa5V3P