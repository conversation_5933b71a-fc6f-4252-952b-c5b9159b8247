package prototype_manage

import (
	"marketing-app/internal/cache"
	handler "marketing-app/internal/handler/prototype/prototype_manage"
	"marketing-app/internal/pkg/db"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	warrantyBaseRepo "marketing-app/internal/repository/warranty/base"
	svc "marketing-app/internal/service/prototype/prototype_manage"

	"github.com/gin-gonic/gin"
)

type PrototypeManageRouter struct {
	prototypeHandler handler.Prototype
}

func NewPrototypeManageRouter() *PrototypeManageRouter {
	database, _ := db.GetDB()
	ptRepo := prototypeRepo.NewPrototypeRepo(database)
	wRepo := warrantyBaseRepo.NewWarranty(database)
	epRepo := endpointRepo.NewEndpoint(database)
	mtRepo := machineRepo.NewMachine(database)
	ptCache := cache.NewPrototypeCache()
	prototypeSvc := svc.NewPrototype(ptRepo, wRepo, epRepo, mtRepo, ptCache)
	return &PrototypeManageRouter{
		prototypeHandler: handler.NewPrototypeHandler(prototypeSvc),
	}
}

func (p *PrototypeManageRouter) Register(r *gin.RouterGroup) {
	g := r.Group("/prototype_manage")
	{
		g.POST("/create", p.prototypeHandler.CreatePrototype)
		g.GET("/list", p.prototypeHandler.ListPrototype)
		g.POST("/delete", p.prototypeHandler.DeletePrototype)
	}
}
