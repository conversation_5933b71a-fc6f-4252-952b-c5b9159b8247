package bugfree_convertor

import (
	"marketing-app/internal/router/bugfree/client"
	"marketing-app/internal/service/bugfree/entity"
)

type BugfreeUserConvertor struct{}

func (b *BugfreeUserConvertor) ClientToEntity(req *client.GetBugfreeUserRequest) *entity.BugfreeUser {
	return &entity.BugfreeUser{
		Uid:      req.Uid,
		Username: req.Username,
	}
}

type BugfreeCategoryConvertor struct{}

func (b *BugfreeCategoryConvertor) ClientToEntity(req *client.BugfreeCategoryRequest) *entity.BugfreeCategory {
	if req.Version == 0 {
		req.Version = 2 // 默认值
	}
	return &entity.BugfreeCategory{
		Uid:     req.Uid,
		Version: req.Version,
	}
}

type BugfreeSearchConvertor struct{}

func (b *BugfreeSearchConvertor) ClientToEntity(req *client.BugfreeSearchRequest) *entity.BugfreeSearch {
	return &entity.BugfreeSearch{
		Uid:     req.Uid,
		Page:    req.Page,
		Count:   req.Count,
		Origin:  req.Origin,
		Keyword: req.Keyword,
	}
}

type BugfreeInfoConvertor struct{}

func (b *BugfreeInfoConvertor) ClientToEntity(req *client.BugfreeInfoRequest) *entity.BugfreeInfo {
	return &entity.BugfreeInfo{
		Uid: req.Uid,
		Id:  req.Id,
	}
}

type BugfreeRepliesConvertor struct{}

func (b *BugfreeRepliesConvertor) ClientToEntity(req *client.BugfreeRepliesRequest) *entity.BugfreeReplies {
	return &entity.BugfreeReplies{
		Uid:   req.Uid,
		Page:  req.Page,
		Count: req.Count,
		Id:    req.Id,
	}
}

type BugfreeReplyConvertor struct{}

func (b *BugfreeReplyConvertor) ClientToEntity(req *client.BugfreeReplyRequest) *entity.BugfreeReply {
	return &entity.BugfreeReply{
		Uid:     req.Uid,
		Id:      req.Id,
		Content: req.Content,
	}
}

type BugfreeDuplicateReplyConvertor struct{}

func (b *BugfreeDuplicateReplyConvertor) ClientToEntity(req *client.BugfreeDuplicateReplyRequest) *entity.BugfreeDuplicateReply {
	// 转换附件列表
	var attachments []entity.Attachment
	for _, att := range req.Attachments {
		attachments = append(attachments, entity.Attachment{
			Name: att.Name,
			Mime: att.Mime,
			Size: att.Size,
			URL:  att.URL,
		})
	}

	return &entity.BugfreeDuplicateReply{
		Uid:         req.Uid,
		Id:          req.Id,
		UserID:      req.Uid, // 使用Uid作为UserID
		Username:    req.Username,
		PhoneNumber: req.PhoneNumber,
		Content:     req.Content,
		Attachments: attachments,
	}
}

type BugfreeRepeatConvertor struct{}

func (b *BugfreeRepeatConvertor) ClientToEntity(req *client.BugfreeRepeatRequest) *entity.BugfreeRepeat {
	return &entity.BugfreeRepeat{
		Id:  req.Id,
		UID: req.UID,
	}
}
