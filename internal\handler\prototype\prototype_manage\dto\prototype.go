package dto

type CreatePrototypeResp struct {
	Message string `json:"message"`
}

type PrototypeListResp struct {
	Data          []map[string]interface{} `json:"data"`
	Size          int                      `json:"size"`
	IsEnd         bool                     `json:"isEnd"`
	ModelCategory []map[string]interface{} `json:"model_category"`
}

type DeletePrototypeResp struct {
	Message string `json:"message"`
}
