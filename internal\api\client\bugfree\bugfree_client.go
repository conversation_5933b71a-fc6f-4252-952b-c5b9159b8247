package bugfree

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"marketing-app/internal/api/client/bugfree/config"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/service/bugfree/entity"
	"net/http"
	"net/url"
	"strconv"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type BugfreeClient struct {
	httpClient *http.Client
	cfg        *config.AppConfig
}

func NewBugfreeClient(cfg *config.AppConfig) BugfreeClient {
	return BugfreeClient{
		httpClient: &http.Client{
			Timeout: cfg.HTTPTimeout,
		},
		cfg: cfg,
	}
}

func (b *BugfreeClient) GetUser(c *gin.Context, uid int, username string) (interface{}, error) {
	// 构建请求参数
	form := b.getUserParam(uid, username)
	requestURL := b.cfg.Host + "/v1/users?"

	// 创建POST请求
	req, err := http.NewRequestWithContext(c, http.MethodPost, requestURL, bytes.NewBufferString(form.Encode()))
	if err != nil {
		err = errors.Wrap(err, "创建请求失败")
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := b.httpClient.Do(req)
	if err != nil {
		err = errors.Wrap(err, "调用Bugfree API失败")
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		err = errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
		return nil, err
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		err = errors.Wrap(err, "读取响应失败")
		return nil, err
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		err = errors.Wrap(err, "解析响应失败")
		return nil, err
	}

	// 检查响应状态
	if result["code"].(float64) == 0 && result["data"] != nil {
		return result["data"], nil
	}

	return nil, errors.New("未找到用户数据")
}

func (b *BugfreeClient) GetBugfreeCategory(c *gin.Context, reqEntity *entity.BugfreeCategory, roleKey map[string]struct{}) (interface{}, error) {
	var fullUrl string
	form := url.Values{}
	if _, exist := roleKey["afterSalesEngineer"]; !exist {
		fullUrl = b.cfg.Host + "/repair/v1/products"
	} else {
		fullUrl = b.cfg.Host + "/v2/products"
		form.Set("version", strconv.Itoa(reqEntity.Version))
	}
	req, err := http.NewRequestWithContext(c, http.MethodPost, fullUrl, bytes.NewBufferString(form.Encode()))
	if err != nil {
		err = errors.Wrap(err, "创建请求失败")
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := b.httpClient.Do(req)
	if err != nil {
		err = errors.Wrap(err, "调用Bugfree API失败")
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		err = errors.Wrap(err, "读取响应失败")
		return nil, err
	}

	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		err = errors.Wrap(err, "解析响应失败")
		return nil, err
	}

	if result["code"].(float64) == 0 && result["data"] != nil {
		return result["data"], nil
	}

	return nil, errors.Wrap(err, "调用BugFree API 返回错误")
}

func (b *BugfreeClient) GetBugfreeSearch(c *gin.Context, reqEntity *entity.BugfreeSearch, roleKey map[string]struct{}) (interface{}, error) {
	var fullUrl string

	// 根据角色和origin确定API端点
	if _, exist := roleKey["afterSalesEngineer"]; exist {
		fullUrl = b.cfg.Host + "/repair/v1/bugs"
		if reqEntity.Origin == 1 { // 是维修角色但是进入的是用户反馈界面
			fullUrl = b.cfg.Host + "/v1/bugs"
		}
	} else {
		if reqEntity.Origin == 2 { // 是终端角色但是进入维修反馈界面
			return nil, errors.New("权限不足")
		}
		fullUrl = b.cfg.Host + "/v1/bugs"
	}

	// 分页处理
	if reqEntity.Count > 100 {
		reqEntity.Count = 100
	}
	offset := (reqEntity.Page - 1) * reqEntity.Count
	if offset <= 0 {
		offset = 0
	}

	// 构建请求参数
	params := url.Values{}
	params.Set("keyword", reqEntity.Keyword)
	params.Set("offset", strconv.Itoa(offset))
	params.Set("limit", strconv.Itoa(reqEntity.Count+1))

	// 创建GET请求
	req, err := http.NewRequestWithContext(c, http.MethodGet, fullUrl+"?"+params.Encode(), nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := b.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析响应失败")
	}
	log.Info("result", zap.Any("result", result))
	// 检查响应状态
	if result["code"].(float64) == 0 && result["data"] != nil {
		data := result["data"].([]interface{})
		l := len(data)
		isEnd := l < reqEntity.Count+1
		size := l
		if !isEnd {
			size = reqEntity.Count
			data = data[0:size]
		}

		return map[string]interface{}{
			"data":  data,
			"size":  size,
			"isEnd": isEnd,
		}, nil
	}

	return nil, errors.New("未找到搜索数据")
}

func (b *BugfreeClient) GetBugfreeInfo(c *gin.Context, reqEntity *entity.BugfreeInfo, roleKey map[string]struct{}) (interface{}, error) {
	var fullUrl string

	// 根据角色确定API端点
	if _, exist := roleKey["afterSalesEngineer"]; exist {
		fullUrl = b.cfg.Host + "/repair/v1/bugs/" + strconv.Itoa(reqEntity.Id)
	} else {
		fullUrl = b.cfg.Host + "/v2/bugs/" + strconv.Itoa(reqEntity.Id)
	}

	// 构建请求参数
	params := url.Values{}
	params.Set("user_id", strconv.Itoa(reqEntity.Uid))

	// 创建GET请求
	req, err := http.NewRequestWithContext(c, http.MethodGet, fullUrl+"?"+params.Encode(), nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := b.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析响应失败")
	}
	log.Info("result", zap.Any("result", result))
	// 检查响应状态
	if result["code"].(float64) == 0 && result["data"] != nil {
		return result["data"], nil
	}

	return nil, errors.New("未找到反馈详情数据")
}

func (b *BugfreeClient) GetBugfreeReplies(c *gin.Context, bugId int, page int, count int, roleKey map[string]struct{}) (interface{}, error) {
	var fullUrl string
	// 根据角色确定API端点
	if _, exist := roleKey["afterSalesEngineer"]; exist {
		fullUrl = b.cfg.Host + "/repair/v1/bugs/" + strconv.Itoa(bugId) + "/replies"
	} else {
		fullUrl = b.cfg.Host + "/v1/bugs/" + strconv.Itoa(bugId) + "/replies"
	}

	// 分页处理
	if count > 100 {
		count = 100
	}
	offset := (page - 1) * count
	if offset <= 0 {
		offset = 0
	}

	// 构建请求参数
	params := url.Values{}
	params.Set("offset", strconv.Itoa(offset))
	params.Set("limit", strconv.Itoa(count+1))

	// 创建GET请求
	req, err := http.NewRequestWithContext(c, http.MethodGet, fullUrl+"?"+params.Encode(), nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := b.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析响应失败")
	}
	log.Info("result", zap.Any("result", result))

	// 检查响应状态
	if result["code"].(float64) == 0 && result["data"] != nil {
		data := result["data"].([]interface{})
		l := len(data)
		isEnd := l < count+1
		size := l
		if !isEnd {
			size = count
			data = data[0:size]
		}

		return map[string]interface{}{
			"data":  data,
			"size":  size,
			"isEnd": isEnd,
		}, nil
	}

	return nil, errors.New("未找到回复数据")
}

func (b *BugfreeClient) GetBugfreeProcessRoutes(c *gin.Context, bugId int, roleKey map[string]struct{}) (interface{}, error) {
	var fullUrl string

	// 根据角色确定API端点
	if _, exist := roleKey["afterSalesEngineer"]; exist {
		fullUrl = b.cfg.Host + "/repair/v1/bugs/" + strconv.Itoa(bugId) + "/process-routes"
	} else {
		fullUrl = b.cfg.Host + "/v1/bugs/" + strconv.Itoa(bugId) + "/process-routes"
	}

	// 创建GET请求
	req, err := http.NewRequestWithContext(c, http.MethodGet, fullUrl, nil)
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}

	// 发送请求
	resp, err := b.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析响应失败")
	}

	// 检查响应状态
	if result["code"].(float64) == 0 && result["data"] != nil {
		return result["data"], nil
	}

	return nil, errors.New("未找到处理流程数据")
}

// ReplyBugfree 提交回复
func (b *BugfreeClient) ReplyBugfree(c *gin.Context, bugId int, uid int, content string, roleKey map[string]struct{}) (int, error) {
	var fullUrl string
	if _, exist := roleKey["afterSalesEngineer"]; exist {
		fullUrl = b.cfg.Host + "/repair/v1/bugs/" + strconv.Itoa(bugId) + "/replies"
	} else {
		fullUrl = b.cfg.Host + "/v1/bugs/" + strconv.Itoa(bugId) + "/replies"
	}

	form := url.Values{}
	form.Set("user_id", strconv.Itoa(uid))
	form.Set("content", content)

	req, err := http.NewRequestWithContext(c, http.MethodPost, fullUrl, bytes.NewBufferString(form.Encode()))
	if err != nil {
		return -1, errors.Wrap(err, "创建请求失败")
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := b.httpClient.Do(req)
	if err != nil {
		return -1, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) { _ = Body.Close() }(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return -1, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return -1, errors.Wrap(err, "读取响应失败")
	}

	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return -1, errors.Wrap(err, "解析响应失败")
	}
	log.Info("result", zap.Any("result", result))
	if code, ok := result["code"].(float64); ok && code == 0 {
		return 0, nil
	}
	if errVal, ok := result["error"]; ok {
		return 2, fmt.Errorf("系统错误：%v", errVal)
	}
	return -1, errors.New("系统错误")
}

// DuplicateReplyBugfree 重复问题回复
func (b *BugfreeClient) DuplicateReplyBugfree(c *gin.Context, bugId int, content map[string]interface{}, roleKey map[string]struct{}) (interface{}, error) {
	var fullUrl string
	if _, exist := roleKey["afterSalesEngineer"]; exist {
		fullUrl = b.cfg.Host + "/repair/v1/bugs/" + strconv.Itoa(bugId) + "/duplicate-reply"
	} else {
		fullUrl = b.cfg.Host + "/v1/bugs/" + strconv.Itoa(bugId) + "/duplicate-reply"
	}

	// 将content转换为JSON
	jsonData, err := json.Marshal(content)
	if err != nil {
		return nil, errors.Wrap(err, "序列化请求数据失败")
	}

	req, err := http.NewRequestWithContext(c, http.MethodPost, fullUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := b.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) { _ = Body.Close() }(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析响应失败")
	}
	log.Info("result", zap.Any("result", result))

	if code, ok := result["code"].(float64); ok && code == 0 && result["data"] != nil {
		return result["data"], nil
	}

	return nil, errors.New("重复回复失败")
}

// RepeatBugfree 重复问题
func (b *BugfreeClient) RepeatBugfree(c *gin.Context, bugId int, content map[string]interface{}) (interface{}, error) {
	// 固定使用 v1 端点，不需要角色判断
	fullUrl := b.cfg.Host + "/v1/bugs/" + strconv.Itoa(bugId) + "/duplications"

	// 将content转换为JSON
	jsonData, err := json.Marshal(content)
	if err != nil {
		return nil, errors.Wrap(err, "序列化请求数据失败")
	}

	req, err := http.NewRequestWithContext(c, http.MethodPost, fullUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, errors.Wrap(err, "创建请求失败")
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := b.httpClient.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "调用Bugfree API失败")
	}
	defer func(Body io.ReadCloser) { _ = Body.Close() }(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("Bugfree API响应错误，状态码: %d", resp.StatusCode))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "读取响应失败")
	}

	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		return nil, errors.Wrap(err, "解析响应失败")
	}
	log.Info("result", zap.Any("result", result))

	if code, ok := result["code"].(float64); ok && code == 0 && result["data"] != nil {
		return result["data"], nil
	}

	return nil, errors.New("重复问题失败")
}

func (b *BugfreeClient) getUserParam(uid int, username string) url.Values {
	params := url.Values{}
	params.Set("user_id", strconv.Itoa(uid))
	params.Set("username", username)
	params.Set("origin", "care")

	return params
}
