package prototype_manage

import (
	"fmt"
	"github.com/pkg/errors"
	"marketing-app/internal/api/client/binding"
	"marketing-app/internal/api/client/binding/config"
	"marketing-app/internal/cache"
	"marketing-app/internal/consts"
	"marketing-app/internal/pkg/utils"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	prototypeRepo "marketing-app/internal/repository/prototype"
	prototypeBuilder "marketing-app/internal/repository/prototype/builder"
	"marketing-app/internal/repository/prototype/dto"
	warrantyBaseRepo "marketing-app/internal/repository/warranty/base"
	"marketing-app/internal/repository/warranty/base/builder"
	warrantyBaseDto "marketing-app/internal/repository/warranty/base/dto"
	"marketing-app/internal/service/prototype/prototype_manage/entity"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PrototypeSvc interface {
	CreatePrototype(c *gin.Context, param *entity.Prototype) (err error)
	GetPrototypeListData(c *gin.Context, param *entity.ListPrototype) ([]dto.ModelCategory, *dto.PrototypeListResult, error)
	DeletePrototype(c *gin.Context, param *entity.DeletePrototype) (msg string, err error)
}

type prototype struct {
	repo           prototypeRepo.Prototype
	warrantyRepo   warrantyBaseRepo.Warranty
	endpointRepo   endpointRepo.Endpoint
	drpMachineRepo machineRepo.Machine
	prototypeCache cache.PrototypeCache
	bindingClient  binding.BindingClient
}

func NewPrototype(
	repo prototypeRepo.Prototype,
	warrantyRepo warrantyBaseRepo.Warranty,
	endpointRepo endpointRepo.Endpoint,
	drpMachineRepo machineRepo.Machine,
	prototypeCache cache.PrototypeCache,
) PrototypeSvc {
	cfg := config.LoadBindingConfig()
	bindingClient := binding.NewBindingClient(cfg)

	return &prototype{
		repo:           repo,
		warrantyRepo:   warrantyRepo,
		endpointRepo:   endpointRepo,
		drpMachineRepo: drpMachineRepo,
		prototypeCache: prototypeCache,
		bindingClient:  bindingClient,
	}
}

func (p *prototype) CreatePrototype(c *gin.Context, param *entity.Prototype) (err error) {
	warranty, err := p.checkWarranty(c, param.Barcode)
	if err != nil {
		return
	}
	endpointAgencyInfo, err := p.endpointRepo.GetEndpointAgency(c, param.EndpointId)
	if err != nil {
		return
	}
	prototypeInfo, err := p.checkPrototypeEntry(c, param.Barcode, param.EndpointId, endpointAgencyInfo.TopAgency)
	if err != nil {
		return
	}
	// 退货机器不可录入
	retRec, err := p.repo.GetLatestWarrantyReturn(c, param.Barcode)
	if err == nil && retRec != nil && retRec.ReturnAt != nil {
		err = errors.New("此机器已经被退过机，无法设置为样机")
		return
	}
	if prototypeInfo == nil {
		if warranty.Id != 0 && warranty.Status == 0 && warranty.ActivatedAtOld != nil && warranty.CategoryId == 1 {
			err = errors.New("此机器已经被激活，无法设置为样机")
			return
		}
	}
	if prototypeInfo != nil && prototypeInfo.Type == 0 && prototypeInfo.CategoryID == 1 {
		if warranty.Id != 0 && warranty.Status == 0 && warranty.ActivatedAtOld != nil && warranty.CategoryId == 1 {
			err = errors.New("此机器已经被激活，无法设置为样机")
			return
		}
	}
	// 样机限制
	limit := 20
	limitInfo, _ := p.repo.GetPrototypeLimit(c, param.EndpointId)
	if limitInfo != nil && limitInfo.PrototypeLimit != nil && limitInfo.PrototypeFrequencyLimit != nil {
		if *limitInfo.PrototypeLimit != -1 {
			limit = *limitInfo.PrototypeLimit
		}
	}
	total, _, err := p.repo.GetPrototypeCount(c, param.EndpointId)
	if err != nil {
		err = errors.New("系统错误")
		return
	}
	if total >= int64(limit) {
		err = fmt.Errorf("在库样机数已到达最大限制: %d 台", limit)
		return
	}
	// MES 机器信息
	mes, err := utils.CheckMachine(utils.CheckMachineParams{
		Barcode: param.Barcode,
	})
	if err != nil || mes == nil {
		err = errors.New("此条码不可作为样机")
		return
	}
	// 机型可做样机校验
	modelID, _ := strconv.Atoi(mes["model_id"].(string))
	ok, err := p.repo.ModelCanBePrototype(c, modelID)
	if err != nil || !ok {
		err = errors.New("此机型不可作为样机")
		return
	}
	// 新增样机
	err = p.repo.CreateTransaction(c, param.Barcode, param.EndpointId, param.Uid, prototypeInfo, mes, p.drpMachineRepo)
	if err != nil {
		err = errors.New("系统错误")
		return
	}
	// 设置缓存
	_ = p.prototypeCache.Set(c, mes["number"].(string))
	// 同步进销存
	err = p.drpMachineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(param.Barcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(4).
			ReturnEq(0),
	)
	if err != nil {
		err = errors.New("系统错误")
		return
	}
	return nil
}

func (p *prototype) GetPrototypeListData(c *gin.Context, param *entity.ListPrototype) ([]dto.ModelCategory, *dto.PrototypeListResult, error) {
	// 获取机型分类
	modelCategory, err := p.repo.GetPrototypeMachineCategory(c)
	if err != nil {
		return nil, nil, err
	}
	// 获取样机列表
	data, err := p.repo.GetPrototypeList(c, param.EndpointId, param.PageSize, param.Page)
	if err != nil {
		return nil, nil, err
	}

	return modelCategory, data, nil
}

func (p *prototype) DeletePrototype(c *gin.Context, param *entity.DeletePrototype) (msg string, err error) {
	// 获取样机信息
	prototypeInfo, err := p.repo.GetPrototypeInfo(c, param.Barcode)
	if err != nil {
		return
	}
	if prototypeInfo == nil {
		err = errors.New("此机器非样机，无需离库")
		return
	}
	// 权限验证
	if prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.Discontinued == 0 {
		err = errors.New("演示样机，无权操作")
		return
	}
	if prototypeInfo.Endpoint != param.EndpointId {
		err = errors.New("非此终端机器，无权操作")
		return
	}
	// 删除样机（离库）
	_, msg, err = p.repo.DeletePrototype(c, param.Barcode, param.EndpointId)
	if err != nil {
		err = errors.Wrap(err, "删除样机失败")
		return
	}
	// 删除缓存
	err = p.prototypeCache.Del(c, prototypeInfo.Number)
	if err != nil {
		err = errors.New("删除样机成功，更新缓存失败")
	}
	// 如果是教育类设备则需要取消绑定
	if prototypeInfo.CategoryID == consts.ModelCategoryStudentPad {
		success, err := p.bindingClient.CancelBinding(c, prototypeInfo.Number)
		if err != nil {
			return "系统错误", err
		}
		if success {
			msg = "样机离库成功！此设备的家长助手绑定信息和使用记录已被重置"
		}
	}
	//同步进销存
	err = p.drpMachineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(param.Barcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(5).
			ReturnEq(0),
	)
	if err != nil {
		err = errors.New("同步进销存失败")
		return
	}

	return msg, nil
}

func (p *prototype) checkWarranty(c *gin.Context, barcode string) (warranty warrantyBaseDto.WarrantyMachineType, err error) {
	warranty, err = p.warrantyRepo.GetWarrantyMachineType(
		c,
		builder.NewWarranty().
			JoinMachineType("LEFT JOIN machine_type mt ON warranty.model_id = mt.model_id").
			BarcodeEq(barcode).
			StatusIn(consts.WarrantyStatusActive, consts.WarrantyStatusVirtual),
	)
	if err != nil {
		return
	}
	if warranty.Id != 0 && warranty.Status == consts.WarrantyStatusActive {
		err = errors.New("已有保卡")
		return
	}
	return warranty, nil
}

func (p *prototype) checkPrototypeEntry(c *gin.Context, barcode string, endpointId int, endpointTopAgency int) (prototypeInfo *dto.PrototypeMachineTypeConfig, err error) {
	prototypeInfo, err = p.repo.GetPrototypeMachineTypeConfig(
		c,
		prototypeBuilder.NewPrototype().
			JoinPrototypeConfigEq("LEFT JOIN prototype_config pc ON prototype.model_id = pc.model_id").
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(barcode). // 判断是否是演示样机
			StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.TopAgency > 0 && prototypeInfo.TopAgency != endpointTopAgency {
		err = errors.New("出货代理不对，无法设置为样机")
		return nil, err
	}
	if prototypeInfo.Status == consts.WarrantyStatusActive && prototypeInfo.Endpoint == endpointId {
		err = errors.New("此终端已录入过此样机")
		return nil, err
	}
	return prototypeInfo, err
}
